import { Routes, Route } from 'react-router-dom'
import { AuthProvider } from './contexts/AuthContext'
import { Layout } from './components/Layout'
import { Dashboard } from './pages/Dashboard'
import { RouteAnalysis } from './pages/RouteAnalysis'
import { RiskMap } from './pages/RiskMap'
import { WeatherAlerts } from './pages/WeatherAlerts'
import { Login } from './pages/Login'
import { NotFound } from './pages/NotFound'
import { ProtectedRoute } from './components/ProtectedRoute'

function App() {
  return (
    <AuthProvider>
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Dashboard />} />
          <Route path="route-analysis" element={<RouteAnalysis />} />
          <Route path="risk-map" element={<RiskMap />} />
          <Route path="weather-alerts" element={<WeatherAlerts />} />
        </Route>
        <Route path="*" element={<NotFound />} />
      </Routes>
    </AuthProvider>
  )
}

export default App
