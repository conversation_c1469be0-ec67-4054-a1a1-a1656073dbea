"""
Health check endpoints for FleetSentinel application.
"""
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
import redis
from datetime import datetime
from typing import Dict, Any

from ..database import get_db, get_redis, check_db_connection, check_redis_connection
from ..config import settings

router = APIRouter()


@router.get("/")
async def health_check():
    """
    Basic health check endpoint.
    """
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": settings.app_name,
        "version": settings.app_version
    }


@router.get("/detailed")
async def detailed_health_check(
    db: Session = Depends(get_db),
    redis_client: redis.Redis = Depends(get_redis)
) -> Dict[str, Any]:
    """
    Detailed health check including database and Redis connectivity.
    """
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": settings.app_name,
        "version": settings.app_version,
        "checks": {}
    }
    
    # Database check
    try:
        db.execute("SELECT 1")
        health_status["checks"]["database"] = {
            "status": "healthy",
            "message": "Database connection successful"
        }
    except Exception as e:
        health_status["status"] = "unhealthy"
        health_status["checks"]["database"] = {
            "status": "unhealthy",
            "message": f"Database connection failed: {str(e)}"
        }
    
    # Redis check
    try:
        redis_client.ping()
        health_status["checks"]["redis"] = {
            "status": "healthy",
            "message": "Redis connection successful"
        }
    except Exception as e:
        health_status["checks"]["redis"] = {
            "status": "unhealthy",
            "message": f"Redis connection failed: {str(e)}"
        }
    
    # PostGIS check
    try:
        result = db.execute("SELECT PostGIS_Version()").fetchone()
        health_status["checks"]["postgis"] = {
            "status": "healthy",
            "message": f"PostGIS available: {result[0] if result else 'Unknown version'}"
        }
    except Exception as e:
        health_status["checks"]["postgis"] = {
            "status": "unhealthy",
            "message": f"PostGIS check failed: {str(e)}"
        }
    
    return health_status


@router.get("/ready")
async def readiness_check():
    """
    Kubernetes readiness probe endpoint.
    """
    if check_db_connection():
        return {"status": "ready"}
    else:
        return {"status": "not ready"}, 503


@router.get("/live")
async def liveness_check():
    """
    Kubernetes liveness probe endpoint.
    """
    return {"status": "alive"}
