"""
FleetSentinel FastAPI Application
Main entry point for the AI-driven geo-risk dashboard backend.
"""
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import logging
from contextlib import asynccontextmanager

from .config import settings
from .database import init_db, check_db_connection, check_redis_connection
from .routes import health, auth, routes as route_routes, risk, weather


# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(settings.log_file),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan events.
    """
    # Startup
    logger.info("Starting FleetSentinel application...")
    
    # Initialize database
    try:
        init_db()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise
    
    # Check connections
    if not check_db_connection():
        logger.error("Database connection check failed")
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    if not check_redis_connection():
        logger.warning("Redis connection check failed - caching will be disabled")
    
    logger.info("FleetSentinel application started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down FleetSentinel application...")


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    description="AI-Driven Geo-Risk Dashboard for fleet safety and route optimization",
    version=settings.app_version,
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"]  # Configure appropriately for production
)


# Exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """
    Custom HTTP exception handler.
    """
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "status_code": exc.status_code
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """
    General exception handler for unhandled exceptions.
    """
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": "Internal server error",
            "status_code": 500
        }
    )


# Include routers
app.include_router(health.router, prefix="/health", tags=["Health"])
app.include_router(auth.router, prefix="/auth", tags=["Authentication"])
app.include_router(route_routes.router, prefix="/routes", tags=["Routes"])
app.include_router(risk.router, prefix="/risk", tags=["Risk Analysis"])
app.include_router(weather.router, prefix="/weather", tags=["Weather"])


@app.get("/")
async def root():
    """
    Root endpoint with application information.
    """
    return {
        "name": settings.app_name,
        "version": settings.app_version,
        "description": "AI-Driven Geo-Risk Dashboard",
        "docs": "/docs",
        "health": "/health"
    }


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload,
        log_level=settings.log_level.lower()
    )
