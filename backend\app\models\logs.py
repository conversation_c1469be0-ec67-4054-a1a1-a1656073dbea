"""
Logging and audit models for FleetSentinel application.
"""
from sqlalchemy import Column, Integer, String, DateTime, Text, Float, Boolean, Index
from geoalchemy2 import Geography
import json

from .base import BaseModel


class RouteAnalysisLog(BaseModel):
    """Model for logging route analysis requests and results."""
    __tablename__ = "route_analysis_logs"
    
    # Request identification
    request_id = Column(String(100), unique=True, nullable=False)
    user_id = Column(Integer)
    session_id = Column(String(255))
    
    # Request details
    start_lat = Column(Float, nullable=False)
    start_lon = Column(Float, nullable=False)
    end_lat = Column(Float, nullable=False)
    end_lon = Column(Float, nullable=False)
    vehicle_id = Column(String(50))
    route_type = Column(String(20))
    
    # Analysis results
    route_id = Column(String(100))
    distance_km = Column(Float)
    duration_minutes = Column(Float)
    risk_score = Column(Float)
    processing_time_ms = Column(Integer)
    
    # Status
    status = Column(String(20), default='pending')  # pending, completed, failed
    error_message = Column(Text)
    
    # Request metadata
    ip_address = Column(String(45))
    user_agent = Column(Text)
    api_version = Column(String(10))
    
    # Indexes
    __table_args__ = (
        Index('idx_route_logs_request_id', request_id),
        Index('idx_route_logs_user', user_id),
        Index('idx_route_logs_status', status),
        Index('idx_route_logs_created', 'created_at'),
    )


class SystemLog(BaseModel):
    """Model for system-level logging."""
    __tablename__ = "system_logs"
    
    # Log identification
    log_level = Column(String(10), nullable=False)  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    logger_name = Column(String(100), nullable=False)
    
    # Log content
    message = Column(Text, nullable=False)
    module = Column(String(100))
    function = Column(String(100))
    line_number = Column(Integer)
    
    # Context
    user_id = Column(Integer)
    session_id = Column(String(255))
    request_id = Column(String(100))
    
    # Additional data
    extra_data = Column(Text)  # JSON extra data
    stack_trace = Column(Text)
    
    # Indexes
    __table_args__ = (
        Index('idx_system_logs_level', log_level),
        Index('idx_system_logs_logger', logger_name),
        Index('idx_system_logs_created', 'created_at'),
        Index('idx_system_logs_user', user_id),
    )


class ApiUsageLog(BaseModel):
    """Model for tracking API usage and performance."""
    __tablename__ = "api_usage_logs"
    
    # Request identification
    request_id = Column(String(100), unique=True, nullable=False)
    user_id = Column(Integer)
    
    # Request details
    method = Column(String(10), nullable=False)  # GET, POST, PUT, DELETE
    endpoint = Column(String(200), nullable=False)
    query_params = Column(Text)  # JSON query parameters
    request_body_size = Column(Integer)
    
    # Response details
    status_code = Column(Integer, nullable=False)
    response_body_size = Column(Integer)
    processing_time_ms = Column(Integer, nullable=False)
    
    # Client information
    ip_address = Column(String(45))
    user_agent = Column(Text)
    referer = Column(String(500))
    
    # Rate limiting
    rate_limit_remaining = Column(Integer)
    rate_limit_reset = Column(DateTime(timezone=True))
    
    # Indexes
    __table_args__ = (
        Index('idx_api_logs_endpoint', endpoint),
        Index('idx_api_logs_user', user_id),
        Index('idx_api_logs_status', status_code),
        Index('idx_api_logs_created', 'created_at'),
        Index('idx_api_logs_performance', processing_time_ms),
    )


class DataProcessingLog(BaseModel):
    """Model for logging data processing tasks (satellite, weather, etc.)."""
    __tablename__ = "data_processing_logs"
    
    # Task identification
    task_id = Column(String(100), unique=True, nullable=False)
    task_type = Column(String(50), nullable=False)  # satellite_fetch, weather_update, risk_analysis
    
    # Task details
    source = Column(String(100))  # ESA, OpenWeather, etc.
    source_id = Column(String(100))
    parameters = Column(Text)  # JSON task parameters
    
    # Processing results
    status = Column(String(20), default='pending')  # pending, running, completed, failed
    progress_percent = Column(Float, default=0.0)
    records_processed = Column(Integer, default=0)
    records_created = Column(Integer, default=0)
    records_updated = Column(Integer, default=0)
    records_failed = Column(Integer, default=0)
    
    # Timing
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    processing_time_seconds = Column(Float)
    
    # Error handling
    error_message = Column(Text)
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    
    # Resource usage
    memory_usage_mb = Column(Float)
    cpu_usage_percent = Column(Float)
    
    # Indexes
    __table_args__ = (
        Index('idx_data_logs_task_id', task_id),
        Index('idx_data_logs_type', task_type),
        Index('idx_data_logs_status', status),
        Index('idx_data_logs_created', 'created_at'),
    )
