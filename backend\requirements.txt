# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# Database and ORM
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
geoalchemy2==0.14.2

# Pydantic for data validation
pydantic==2.5.0
pydantic-settings==2.1.0

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# HTTP client for external APIs
httpx==0.25.2
requests==2.31.0

# Geospatial libraries
rasterio==1.3.9
gdal==3.7.3
shapely==2.0.2
pyproj==3.6.1
geopy==2.4.1

# Scientific computing
numpy==1.25.2
pandas==2.1.4
scipy==1.11.4

# Machine Learning (optional for Phase 2)
scikit-learn==1.3.2
xgboost==2.0.2

# Caching
redis==5.0.1
python-redis-lock==4.0.0

# Environment and configuration
python-dotenv==1.0.0

# Logging and monitoring
structlog==23.2.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# Development tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Date and time handling
python-dateutil==2.8.2

# File handling
aiofiles==23.2.1

# Background tasks
celery==5.3.4

# API documentation
python-markdown==3.5.1
