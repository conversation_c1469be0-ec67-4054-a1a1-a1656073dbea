# FleetSentinel - AI-Driven Geo-Risk Dashboard

FleetSentinel is a production-ready AI-powered fleet safety web application that analyzes vehicle routes and returns geo-risk scores, overlays flood zones from satellite imagery, and provides alternate safe routes based on weather and terrain hazards.

## Features

- **Route Risk Analysis**: Analyze vehicle routes and get risk scores (0-100)
- **Satellite Flood Detection**: Real-time flood zone overlays from Sentinel-1 imagery
- **Weather Integration**: Live weather alerts and storm tracking
- **Safe Route Engine**: Alternative route suggestions based on risk analysis
- **Interactive Dashboard**: Real-time map visualization with risk overlays
- **Multi-filter Support**: Filter by weather type, alert severity, and time windows

## Tech Stack

### Frontend
- React + TypeScript
- TailwindCSS for styling
- Mapbox/Leaflet.js for mapping
- Responsive design for mobile and desktop

### Backend
- FastAPI + Python
- PostgreSQL + PostGIS for spatial data
- Rasterio + GDAL for satellite imagery processing
- OpenWeatherMap API integration
- OSRM/GraphHopper for routing

### Infrastructure
- Docker containerization
- GitHub Actions CI/CD
- Production-ready deployment configuration

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for local development)
- Python 3.11+ (for local development)

### Environment Setup
1. Copy `.env.example` to `.env` and fill in your API keys:
```bash
cp .env.example .env
```

2. Start the application with Docker:
```bash
docker-compose up --build
```

3. Access the application:
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

## API Endpoints

- `POST /analyze-route`: Analyze route risk and get recommendations
- `GET /risk-map`: Get risk overlay data for map visualization
- `GET /weather-alerts`: Retrieve live weather alerts for regions
- `GET /safe-routes`: Get alternative safe route suggestions

## Development

### Backend Development
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Frontend Development
```bash
cd frontend
npm install
npm run dev
```

## Testing

Run the test suite:
```bash
# Backend tests
cd backend && python -m pytest

# Frontend tests
cd frontend && npm test
```

## Deployment

The application is containerized and ready for deployment on:
- Azure App Service
- Render
- AWS ECS
- Any Docker-compatible platform

## License

MIT License - see LICENSE file for details.
