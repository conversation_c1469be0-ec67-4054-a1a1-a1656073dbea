<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="FleetSentinel - AI-Driven Geo-Risk Dashboard for fleet safety and route optimization" />
    <meta name="keywords" content="fleet management, route optimization, weather alerts, geo-risk, AI dashboard" />
    <meta name="author" content="FleetSentinel Team" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="FleetSentinel - AI-Driven Geo-Risk Dashboard" />
    <meta property="og:description" content="Analyze vehicle routes, get geo-risk scores, and find safe alternatives with real-time weather and satellite data." />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:title" content="FleetSentinel - AI-Driven Geo-Risk Dashboard" />
    <meta property="twitter:description" content="Analyze vehicle routes, get geo-risk scores, and find safe alternatives with real-time weather and satellite data." />
    
    <!-- Preconnect to external services -->
    <link rel="preconnect" href="https://api.mapbox.com" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet" />
    
    <!-- Mapbox CSS -->
    <link href="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css" rel="stylesheet" />
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <title>FleetSentinel - AI-Driven Geo-Risk Dashboard</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
