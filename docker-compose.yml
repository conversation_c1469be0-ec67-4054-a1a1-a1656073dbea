version: '3.8'

services:
  # PostgreSQL with PostGIS extension
  postgres:
    image: postgis/postgis:15-3.3
    container_name: fleetsentinel_postgres
    environment:
      POSTGRES_DB: fleetsentinel
      POSTGRES_USER: fleetsentinel_user
      POSTGRES_PASSWORD: your_secure_password_here
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - fleetsentinel_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U fleetsentinel_user -d fleetsentinel"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: fleetsentinel_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - fleetsentinel_network
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend FastAPI application
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: fleetsentinel_backend
    environment:
      - DATABASE_URL=***********************************************************************/fleetsentinel
      - REDIS_URL=redis://redis:6379
      - CORS_ORIGINS=http://localhost:3000,http://frontend:3000
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - fleetsentinel_network
    env_file:
      - .env
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend React application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: fleetsentinel_frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - fleetsentinel_network
    env_file:
      - .env

  # OSRM routing server (optional, for local routing)
  osrm:
    image: osrm/osrm-backend:latest
    container_name: fleetsentinel_osrm
    ports:
      - "5000:5000"
    volumes:
      - osrm_data:/data
    networks:
      - fleetsentinel_network
    command: osrm-routed --algorithm mld /data/map.osrm
    profiles:
      - osrm

volumes:
  postgres_data:
  redis_data:
  osrm_data:

networks:
  fleetsentinel_network:
    driver: bridge

profiles:
  osrm:
    # Optional OSRM service for local routing
    # Enable with: docker-compose --profile osrm up
