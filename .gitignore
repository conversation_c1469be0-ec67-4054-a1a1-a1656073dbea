# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Dependencies
node_modules/
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output

# Frontend build outputs
frontend/dist/
frontend/build/

# Backend specific
backend/uploads/
backend/logs/
*.db
*.sqlite3

# Docker
.dockerignore

# Testing
.coverage
htmlcov/
.pytest_cache/
.cache

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Database
*.db
*.sqlite
*.sqlite3
postgres_data/
redis_data/
osrm_data/

# Satellite imagery and large data files
*.tif
*.tiff
*.geotiff
*.hdf
*.nc
data/
satellite_data/

# API keys and secrets (additional protection)
**/secrets/
**/*secret*
**/*key*
!**/public_key*
