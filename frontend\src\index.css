@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS for FleetSentinel */

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 antialiased;
  }
  
  * {
    @apply border-gray-200;
  }
}

@layer components {
  /* Button styles */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 h-10 py-2 px-4;
  }
  
  .btn-secondary {
    @apply btn bg-gray-100 text-gray-900 hover:bg-gray-200 h-10 py-2 px-4;
  }
  
  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 h-10 py-2 px-4;
  }
  
  .btn-sm {
    @apply h-8 px-3 text-xs;
  }
  
  .btn-lg {
    @apply h-12 px-8;
  }
  
  /* Card styles */
  .card {
    @apply bg-white rounded-lg border shadow-sm;
  }
  
  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }
  
  .card-title {
    @apply text-2xl font-semibold leading-none tracking-tight;
  }
  
  .card-description {
    @apply text-sm text-gray-600;
  }
  
  .card-content {
    @apply p-6 pt-0;
  }
  
  .card-footer {
    @apply flex items-center p-6 pt-0;
  }
  
  /* Input styles */
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .label {
    @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
  }
  
  /* Badge styles */
  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }
  
  .badge-default {
    @apply badge border-transparent bg-gray-900 text-gray-50 hover:bg-gray-900/80;
  }
  
  .badge-success {
    @apply badge border-transparent bg-success-500 text-white hover:bg-success-600;
  }
  
  .badge-warning {
    @apply badge border-transparent bg-warning-500 text-white hover:bg-warning-600;
  }
  
  .badge-danger {
    @apply badge border-transparent bg-danger-500 text-white hover:bg-danger-600;
  }
  
  /* Map container */
  .map-container {
    @apply relative w-full h-full rounded-lg overflow-hidden;
  }
  
  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }
  
  /* Risk score indicators */
  .risk-low {
    @apply text-success-600 bg-success-50 border-success-200;
  }
  
  .risk-medium {
    @apply text-warning-600 bg-warning-50 border-warning-200;
  }
  
  .risk-high {
    @apply text-danger-600 bg-danger-50 border-danger-200;
  }
  
  .risk-critical {
    @apply text-red-800 bg-red-100 border-red-300;
  }
}

@layer utilities {
  /* Custom utilities */
  .text-balance {
    text-wrap: balance;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

/* Mapbox GL styles */
.mapboxgl-popup {
  max-width: 300px;
}

.mapboxgl-popup-content {
  @apply rounded-lg shadow-lg;
}

.mapboxgl-popup-close-button {
  @apply text-gray-500 hover:text-gray-700;
}

/* Leaflet styles override */
.leaflet-container {
  @apply rounded-lg;
}

.leaflet-popup-content-wrapper {
  @apply rounded-lg shadow-lg;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
