"""
Database configuration and session management for FleetSentinel.
"""
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from geoalchemy2 import Geography
import redis
from .config import settings

# SQLAlchemy setup
engine = create_engine(
    settings.database_url,
    pool_pre_ping=True,
    pool_recycle=300,
    echo=settings.debug
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for SQLAlchemy models
Base = declarative_base()

# Metadata for spatial operations
metadata = MetaData()

# Redis connection for caching
redis_client = redis.from_url(settings.redis_url, decode_responses=True)


def get_db():
    """
    Dependency to get database session.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_redis():
    """
    Dependency to get Redis client.
    """
    return redis_client


def init_db():
    """
    Initialize database tables.
    """
    Base.metadata.create_all(bind=engine)


def check_db_connection():
    """
    Check if database connection is working.
    """
    try:
        with engine.connect() as connection:
            connection.execute("SELECT 1")
        return True
    except Exception as e:
        print(f"Database connection failed: {e}")
        return False


def check_redis_connection():
    """
    Check if Redis connection is working.
    """
    try:
        redis_client.ping()
        return True
    except Exception as e:
        print(f"Redis connection failed: {e}")
        return False
