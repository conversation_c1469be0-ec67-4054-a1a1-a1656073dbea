import { useState } from 'react'
import { MapPin, Navigation, AlertTriangle } from 'lucide-react'

export function RouteAnalysis() {
  const [routeForm, setRouteForm] = useState({
    startLat: '',
    startLon: '',
    endLat: '',
    endLon: '',
    vehicleId: '',
    routeType: 'safest' as 'fastest' | 'shortest' | 'safest',
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // TODO: Implement route analysis
    console.log('Analyzing route:', routeForm)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Route Analysis</h1>
        <p className="text-gray-600">
          Analyze routes for safety risks and get alternative suggestions
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Route Input Form */}
        <div className="lg:col-span-1">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Route Details</h3>
              <p className="card-description">
                Enter start and end coordinates for analysis
              </p>
            </div>
            <div className="card-content">
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="label">Start Latitude</label>
                    <input
                      type="number"
                      step="any"
                      className="input"
                      placeholder="37.7749"
                      value={routeForm.startLat}
                      onChange={(e) =>
                        setRouteForm({ ...routeForm, startLat: e.target.value })
                      }
                    />
                  </div>
                  <div>
                    <label className="label">Start Longitude</label>
                    <input
                      type="number"
                      step="any"
                      className="input"
                      placeholder="-122.4194"
                      value={routeForm.startLon}
                      onChange={(e) =>
                        setRouteForm({ ...routeForm, startLon: e.target.value })
                      }
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="label">End Latitude</label>
                    <input
                      type="number"
                      step="any"
                      className="input"
                      placeholder="37.7849"
                      value={routeForm.endLat}
                      onChange={(e) =>
                        setRouteForm({ ...routeForm, endLat: e.target.value })
                      }
                    />
                  </div>
                  <div>
                    <label className="label">End Longitude</label>
                    <input
                      type="number"
                      step="any"
                      className="input"
                      placeholder="-122.4094"
                      value={routeForm.endLon}
                      onChange={(e) =>
                        setRouteForm({ ...routeForm, endLon: e.target.value })
                      }
                    />
                  </div>
                </div>

                <div>
                  <label className="label">Vehicle ID (Optional)</label>
                  <input
                    type="text"
                    className="input"
                    placeholder="FLEET-001"
                    value={routeForm.vehicleId}
                    onChange={(e) =>
                      setRouteForm({ ...routeForm, vehicleId: e.target.value })
                    }
                  />
                </div>

                <div>
                  <label className="label">Route Type</label>
                  <select
                    className="input"
                    value={routeForm.routeType}
                    onChange={(e) =>
                      setRouteForm({
                        ...routeForm,
                        routeType: e.target.value as any,
                      })
                    }
                  >
                    <option value="safest">Safest Route</option>
                    <option value="fastest">Fastest Route</option>
                    <option value="shortest">Shortest Route</option>
                  </select>
                </div>

                <button type="submit" className="btn-primary w-full">
                  <Navigation className="h-4 w-4 mr-2" />
                  Analyze Route
                </button>
              </form>
            </div>
          </div>
        </div>

        {/* Map and Results */}
        <div className="lg:col-span-2">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Route Visualization</h3>
              <p className="card-description">
                Interactive map showing route and risk zones
              </p>
            </div>
            <div className="card-content">
              <div className="h-96 bg-gray-100 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">
                    Map will be displayed here
                  </p>
                  <p className="text-sm text-gray-400">
                    Enter route details and click "Analyze Route" to see visualization
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Route Results */}
          <div className="mt-6 card">
            <div className="card-header">
              <h3 className="card-title">Analysis Results</h3>
              <p className="card-description">
                Risk assessment and route recommendations
              </p>
            </div>
            <div className="card-content">
              <div className="text-center py-8">
                <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">
                  No analysis results yet
                </p>
                <p className="text-sm text-gray-400">
                  Submit a route for analysis to see detailed results
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
