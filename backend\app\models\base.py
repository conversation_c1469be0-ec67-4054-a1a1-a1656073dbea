"""
Base model classes for FleetSentinel application.
"""
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>teger, DateTime, String
from sqlalchemy.sql import func
from ..database import Base


class TimestampMixin:
    """Mixin to add timestamp fields to models."""
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())


class BaseModel(Base, TimestampMixin):
    """Base model class with common fields."""
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True)
    
    def to_dict(self):
        """Convert model instance to dictionary."""
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}
