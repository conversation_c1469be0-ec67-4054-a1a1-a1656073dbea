"""
Route and vehicle models for FleetSentinel application.
"""
from sqlalchemy import Column, Integer, String, Float, DateTime, Text, Boolean, ForeignKey, Index
from sqlalchemy.orm import relationship
from geoalchemy2 import Geography
from geoalchemy2.shape import to_shape
import json

from .base import BaseModel


class Vehicle(BaseModel):
    """Model for vehicles in the fleet."""
    __tablename__ = "vehicles"
    
    # Vehicle identification
    vehicle_id = Column(String(50), unique=True, nullable=False)
    license_plate = Column(String(20))
    make = Column(String(50))
    model = Column(String(50))
    year = Column(Integer)
    
    # Vehicle specifications
    vehicle_type = Column(String(30))  # truck, van, car, motorcycle, etc.
    weight_kg = Column(Float)
    height_m = Column(Float)
    length_m = Column(Float)
    width_m = Column(Float)
    
    # Operational status
    is_active = Column(Boolean, default=True)
    current_location = Column(Geography('POINT', srid=4326))
    last_seen = Column(DateTime(timezone=True))
    
    # Driver information
    driver_name = Column(String(100))
    driver_contact = Column(String(100))
    
    # Relationships
    routes = relationship("Route", back_populates="vehicle")
    
    # Indexes
    __table_args__ = (
        Index('idx_vehicles_vehicle_id', vehicle_id),
        Index('idx_vehicles_active', is_active),
        Index('idx_vehicles_location', current_location, postgresql_using='gist'),
    )


class Route(BaseModel):
    """Model for analyzed routes."""
    __tablename__ = "routes"
    
    # Route identification
    route_id = Column(String(100), unique=True, nullable=False)
    
    # Vehicle association
    vehicle_id = Column(Integer, ForeignKey('vehicles.id'))
    vehicle = relationship("Vehicle", back_populates="routes")
    
    # Route geometry
    start_point = Column(Geography('POINT', srid=4326), nullable=False)
    end_point = Column(Geography('POINT', srid=4326), nullable=False)
    route_geometry = Column(Geography('LINESTRING', srid=4326), nullable=False)
    
    # Route metrics
    distance_km = Column(Float, nullable=False)
    duration_minutes = Column(Float, nullable=False)
    risk_score = Column(Float, nullable=False, default=0.0)  # 0-100 scale
    
    # Route type and preferences
    route_type = Column(String(20), default='fastest')  # fastest, shortest, safest
    avoid_tolls = Column(Boolean, default=False)
    avoid_highways = Column(Boolean, default=False)
    
    # Analysis results
    total_segments = Column(Integer, default=0)
    high_risk_segments = Column(Integer, default=0)
    weather_alerts_count = Column(Integer, default=0)
    flood_zones_count = Column(Integer, default=0)
    
    # Status
    status = Column(String(20), default='planned')  # planned, active, completed, cancelled
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    
    # Relationships
    segments = relationship("RouteSegment", back_populates="route", cascade="all, delete-orphan")
    
    # Indexes
    __table_args__ = (
        Index('idx_routes_route_id', route_id),
        Index('idx_routes_vehicle', vehicle_id),
        Index('idx_routes_geometry', route_geometry, postgresql_using='gist'),
        Index('idx_routes_risk_score', risk_score),
        Index('idx_routes_status', status),
    )
    
    def to_geojson(self):
        """Convert route to GeoJSON format."""
        geom = to_shape(self.route_geometry)
        return {
            "type": "Feature",
            "id": self.id,
            "geometry": geom.__geo_interface__,
            "properties": {
                "route_id": self.route_id,
                "distance_km": self.distance_km,
                "duration_minutes": self.duration_minutes,
                "risk_score": self.risk_score,
                "route_type": self.route_type,
                "status": self.status,
                "total_segments": self.total_segments,
                "high_risk_segments": self.high_risk_segments,
                "weather_alerts_count": self.weather_alerts_count,
                "flood_zones_count": self.flood_zones_count,
                "created_at": self.created_at.isoformat(),
                "started_at": self.started_at.isoformat() if self.started_at else None,
                "completed_at": self.completed_at.isoformat() if self.completed_at else None
            }
        }


class RouteSegment(BaseModel):
    """Model for individual route segments with risk analysis."""
    __tablename__ = "route_segments"
    
    # Segment identification
    segment_id = Column(String(100), nullable=False)
    sequence_number = Column(Integer, nullable=False)
    
    # Route association
    route_id = Column(Integer, ForeignKey('routes.id'), nullable=False)
    route = relationship("Route", back_populates="segments")
    
    # Segment geometry
    geometry = Column(Geography('LINESTRING', srid=4326), nullable=False)
    start_point = Column(Geography('POINT', srid=4326), nullable=False)
    end_point = Column(Geography('POINT', srid=4326), nullable=False)
    
    # Segment metrics
    distance_km = Column(Float, nullable=False)
    duration_minutes = Column(Float, nullable=False)
    
    # Risk analysis
    risk_score = Column(Float, nullable=False, default=0.0)
    risk_factors = Column(Text)  # JSON array of risk factors
    
    # Road characteristics
    road_type = Column(String(30))  # highway, arterial, local, etc.
    speed_limit_kmh = Column(Integer)
    surface_type = Column(String(20))  # paved, unpaved, gravel, etc.
    elevation_start_m = Column(Float)
    elevation_end_m = Column(Float)
    
    # Weather and environmental conditions
    weather_risk_score = Column(Float, default=0.0)
    flood_risk_score = Column(Float, default=0.0)
    terrain_risk_score = Column(Float, default=0.0)
    
    # Alerts and warnings
    has_weather_alerts = Column(Boolean, default=False)
    has_flood_zones = Column(Boolean, default=False)
    alert_details = Column(Text)  # JSON array of alerts
    
    # Indexes
    __table_args__ = (
        Index('idx_route_segments_route', route_id, sequence_number),
        Index('idx_route_segments_geometry', geometry, postgresql_using='gist'),
        Index('idx_route_segments_risk', risk_score),
        Index('idx_route_segments_alerts', has_weather_alerts, has_flood_zones),
    )
