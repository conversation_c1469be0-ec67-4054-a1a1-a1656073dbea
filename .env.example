# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=fleetsentinel
POSTGRES_USER=fleetsentinel_user
POSTGRES_PASSWORD=your_secure_password_here

# Database URL for SQLAlchemy
DATABASE_URL=postgresql://fleetsentinel_user:your_secure_password_here@localhost:5432/fleetsentinel

# API Keys
ESA_API_URL=https://scihub.copernicus.eu/
ESA_USERNAME=your_esa_username
ESA_PASSWORD=your_esa_password
OPENWEATHER_API_KEY=your_openweather_api_key_here
MAPBOX_TOKEN=your_mapbox_token_here

# Routing Services
OSRM_URL=http://router.project-osrm.org
GRAPHHOPPER_API_KEY=your_graphhopper_api_key

# JWT Configuration
JWT_SECRET_KEY=your_jwt_secret_key_here_make_it_long_and_secure
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440

# Application Configuration
APP_NAME=FleetSentinel
APP_VERSION=1.0.0
DEBUG=false
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# Redis Configuration (for caching)
REDIS_URL=redis://localhost:6379

# File Storage
UPLOAD_DIR=./uploads
MAX_UPLOAD_SIZE=50MB

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log

# External Services
WEATHER_UPDATE_INTERVAL=300  # seconds
SATELLITE_UPDATE_INTERVAL=3600  # seconds
CACHE_TTL=1800  # seconds

# Development Settings
RELOAD=true
HOST=0.0.0.0
PORT=8000

# Frontend Configuration
REACT_APP_API_URL=http://localhost:8000
REACT_APP_MAPBOX_TOKEN=your_mapbox_token_here
REACT_APP_APP_NAME=FleetSentinel
