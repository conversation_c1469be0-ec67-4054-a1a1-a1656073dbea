import { Route, MapPin, AlertTriangle, TrendingUp } from 'lucide-react'

export function Dashboard() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">
          Overview of fleet safety and risk analysis
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="card">
          <div className="card-content">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Route className="h-6 w-6 text-primary-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Routes
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">1,247</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-6 w-6 text-warning-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Active Alerts
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">23</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <MapPin className="h-6 w-6 text-danger-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    High Risk Zones
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">8</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-6 w-6 text-success-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Avg Risk Score
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">32.5</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Recent Routes</h3>
            <p className="card-description">
              Latest route analyses and risk assessments
            </p>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                      <Route className="h-4 w-4 text-primary-600" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">
                      Route #{1000 + i}
                    </p>
                    <p className="text-sm text-gray-500">
                      Downtown to Airport • Risk Score: {25 + i * 5}
                    </p>
                  </div>
                  <div className="flex-shrink-0">
                    <span className={`badge ${i === 1 ? 'badge-success' : i === 2 ? 'badge-warning' : 'badge-danger'}`}>
                      {i === 1 ? 'Low' : i === 2 ? 'Medium' : 'High'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Critical Alerts</h3>
            <p className="card-description">
              Weather and risk alerts requiring attention
            </p>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              {[
                { type: 'Flood Warning', location: 'Highway 101', severity: 'High' },
                { type: 'Storm Alert', location: 'Downtown Area', severity: 'Medium' },
                { type: 'Road Closure', location: 'Bridge St', severity: 'Critical' },
              ].map((alert, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="h-8 w-8 rounded-full bg-warning-100 flex items-center justify-center">
                      <AlertTriangle className="h-4 w-4 text-warning-600" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">
                      {alert.type}
                    </p>
                    <p className="text-sm text-gray-500">{alert.location}</p>
                  </div>
                  <div className="flex-shrink-0">
                    <span className={`badge ${
                      alert.severity === 'Critical' ? 'badge-danger' : 
                      alert.severity === 'High' ? 'badge-warning' : 'badge-default'
                    }`}>
                      {alert.severity}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
