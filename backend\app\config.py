"""
Configuration settings for FleetSentinel application.
"""
from typing import List, Optional
from pydantic import BaseSettings, validator
import os


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # Application
    app_name: str = "FleetSentinel"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # Server
    host: str = "0.0.0.0"
    port: int = 8000
    reload: bool = False
    
    # Database
    database_url: str
    postgres_host: str = "localhost"
    postgres_port: int = 5432
    postgres_db: str = "fleetsentinel"
    postgres_user: str = "fleetsentinel_user"
    postgres_password: str
    
    # Redis
    redis_url: str = "redis://localhost:6379"
    cache_ttl: int = 1800  # seconds
    
    # JWT Authentication
    jwt_secret_key: str
    jwt_algorithm: str = "HS256"
    jwt_expire_minutes: int = 1440  # 24 hours
    
    # External APIs
    esa_api_url: str = "https://scihub.copernicus.eu/"
    esa_username: Optional[str] = None
    esa_password: Optional[str] = None
    openweather_api_key: str
    mapbox_token: str
    osrm_url: str = "http://router.project-osrm.org"
    graphhopper_api_key: Optional[str] = None
    
    # CORS
    cors_origins: List[str] = ["http://localhost:3000"]
    
    # File handling
    upload_dir: str = "./uploads"
    max_upload_size: str = "50MB"
    
    # Logging
    log_level: str = "INFO"
    log_file: str = "./logs/app.log"
    
    # Background tasks
    weather_update_interval: int = 300  # seconds
    satellite_update_interval: int = 3600  # seconds
    
    @validator('cors_origins', pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    @validator('max_upload_size')
    def parse_upload_size(cls, v):
        """Convert size string to bytes."""
        if isinstance(v, str):
            v = v.upper()
            if v.endswith('MB'):
                return int(v[:-2]) * 1024 * 1024
            elif v.endswith('KB'):
                return int(v[:-2]) * 1024
            elif v.endswith('GB'):
                return int(v[:-2]) * 1024 * 1024 * 1024
        return int(v)
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()
