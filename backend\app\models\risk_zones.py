"""
Risk zone models for FleetSentinel application.
"""
from sqlalchemy import <PERSON>umn, Integer, String, Float, DateTime, Text, Boolean, Index
from geoalchemy2 import Geography, Geometry
from geoalchemy2.shape import to_shape, from_shape
from shapely.geometry import shape
import json

from .base import BaseModel


class RiskZone(BaseModel):
    """Model for storing risk zones (flood zones, weather alerts, etc.)."""
    __tablename__ = "risk_zones"
    
    # Basic information
    zone_type = Column(String(50), nullable=False)  # flood, weather, terrain, etc.
    severity = Column(String(20), nullable=False)   # low, medium, high, critical
    title = Column(String(200), nullable=False)
    description = Column(Text)
    
    # Spatial data - using Geography for lat/lon coordinates (SRID 4326)
    geometry = Column(Geography('POLYGON', srid=4326), nullable=False)
    centroid = Column(Geography('POINT', srid=4326))
    
    # Risk scoring
    risk_score = Column(Float, nullable=False, default=0.0)  # 0-100 scale
    confidence = Column(Float, default=0.8)  # 0-1 scale
    
    # Temporal information
    start_time = Column(DateTime(timezone=True))
    end_time = Column(DateTime(timezone=True))
    is_active = Column(Boolean, default=True)
    
    # Source information
    source = Column(String(100))  # satellite, weather_api, manual, etc.
    source_id = Column(String(100))  # external ID from source system
    metadata = Column(Text)  # JSON metadata
    
    # Spatial indexes
    __table_args__ = (
        Index('idx_risk_zones_geometry', geometry, postgresql_using='gist'),
        Index('idx_risk_zones_centroid', centroid, postgresql_using='gist'),
        Index('idx_risk_zones_type_severity', zone_type, severity),
        Index('idx_risk_zones_active_time', is_active, start_time, end_time),
    )
    
    def to_geojson(self):
        """Convert to GeoJSON format."""
        geom = to_shape(self.geometry)
        return {
            "type": "Feature",
            "id": self.id,
            "geometry": geom.__geo_interface__,
            "properties": {
                "zone_type": self.zone_type,
                "severity": self.severity,
                "title": self.title,
                "description": self.description,
                "risk_score": self.risk_score,
                "confidence": self.confidence,
                "start_time": self.start_time.isoformat() if self.start_time else None,
                "end_time": self.end_time.isoformat() if self.end_time else None,
                "is_active": self.is_active,
                "source": self.source,
                "created_at": self.created_at.isoformat(),
                "updated_at": self.updated_at.isoformat()
            }
        }


class FloodZone(BaseModel):
    """Specialized model for flood zones from satellite imagery."""
    __tablename__ = "flood_zones"
    
    # Satellite imagery information
    satellite_image_id = Column(String(100))
    acquisition_date = Column(DateTime(timezone=True), nullable=False)
    satellite_type = Column(String(50))  # Sentinel-1, Sentinel-2, etc.
    
    # Flood detection parameters
    water_level = Column(Float)  # meters above normal
    flood_extent_km2 = Column(Float)
    detection_confidence = Column(Float, default=0.8)
    
    # Spatial data
    geometry = Column(Geography('POLYGON', srid=4326), nullable=False)
    centroid = Column(Geography('POINT', srid=4326))
    
    # Processing information
    processing_algorithm = Column(String(100))
    ndwi_threshold = Column(Float)  # Normalized Difference Water Index threshold
    
    # Status
    is_verified = Column(Boolean, default=False)
    verification_source = Column(String(100))
    
    # Spatial indexes
    __table_args__ = (
        Index('idx_flood_zones_geometry', geometry, postgresql_using='gist'),
        Index('idx_flood_zones_acquisition', acquisition_date),
        Index('idx_flood_zones_confidence', detection_confidence),
    )


class WeatherAlert(BaseModel):
    """Model for weather alerts and warnings."""
    __tablename__ = "weather_alerts"
    
    # Alert information
    alert_type = Column(String(50), nullable=False)  # storm, flood, wind, etc.
    severity = Column(String(20), nullable=False)    # minor, moderate, severe, extreme
    urgency = Column(String(20), nullable=False)     # immediate, expected, future
    certainty = Column(String(20), nullable=False)   # observed, likely, possible
    
    # Content
    headline = Column(String(200), nullable=False)
    description = Column(Text)
    instruction = Column(Text)
    
    # Temporal
    onset = Column(DateTime(timezone=True))
    expires = Column(DateTime(timezone=True))
    
    # Spatial coverage
    geometry = Column(Geography('POLYGON', srid=4326))
    affected_areas = Column(Text)  # JSON list of area names
    
    # Source
    source_agency = Column(String(100))
    source_id = Column(String(100))
    source_url = Column(String(500))
    
    # Weather parameters
    wind_speed_kmh = Column(Float)
    precipitation_mm = Column(Float)
    temperature_c = Column(Float)
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Spatial indexes
    __table_args__ = (
        Index('idx_weather_alerts_geometry', geometry, postgresql_using='gist'),
        Index('idx_weather_alerts_active', is_active, onset, expires),
        Index('idx_weather_alerts_severity', severity, urgency),
    )
