"""
FleetSentinel Database Models

This module contains all SQLAlchemy models for the FleetSentinel application.
"""

# Import all models to ensure they are registered with SQLAlchemy
from .base import BaseModel, TimestampMixin
from .risk_zones import RiskZone, FloodZone, WeatherAlert
from .routes import Vehicle, Route, RouteSegment
from .users import User, UserSession
from .logs import RouteAnalysisLog, SystemLog, ApiUsageLog, DataProcessingLog

# Export all models for easy importing
__all__ = [
    'BaseModel',
    'TimestampMixin',
    'RiskZone',
    'FloodZone',
    'WeatherAlert',
    'Vehicle',
    'Route',
    'RouteSegment',
    'User',
    'UserSession',
    'RouteAnalysisLog',
    'SystemLog',
    'ApiUsageLog',
    'DataProcessingLog'
]
