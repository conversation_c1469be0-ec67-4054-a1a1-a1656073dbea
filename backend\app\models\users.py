"""
User and authentication models for FleetSentinel application.
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text
from sqlalchemy.orm import relationship
from passlib.context import CryptContext

from .base import BaseModel

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class User(BaseModel):
    """Model for application users."""
    __tablename__ = "users"
    
    # User identification
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(100), unique=True, nullable=False)
    full_name = Column(String(100))
    
    # Authentication
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    
    # Profile information
    role = Column(String(30), default='user')  # user, admin, fleet_manager
    organization = Column(String(100))
    phone = Column(String(20))
    
    # Account status
    email_verified = Column(Boolean, default=False)
    last_login = Column(DateTime(timezone=True))
    failed_login_attempts = Column(Integer, default=0)
    account_locked_until = Column(DateTime(timezone=True))
    
    # Preferences
    preferences = Column(Text)  # JSON preferences
    
    def verify_password(self, password: str) -> bool:
        """Verify password against hash."""
        return pwd_context.verify(password, self.hashed_password)
    
    def set_password(self, password: str):
        """Set password hash."""
        self.hashed_password = pwd_context.hash(password)
    
    @property
    def is_locked(self) -> bool:
        """Check if account is locked."""
        if self.account_locked_until:
            from datetime import datetime
            return datetime.utcnow() < self.account_locked_until
        return False


class UserSession(BaseModel):
    """Model for tracking user sessions."""
    __tablename__ = "user_sessions"
    
    # Session identification
    session_id = Column(String(255), unique=True, nullable=False)
    user_id = Column(Integer, nullable=False)
    
    # Session details
    ip_address = Column(String(45))  # IPv6 compatible
    user_agent = Column(Text)
    
    # Session status
    is_active = Column(Boolean, default=True)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    last_activity = Column(DateTime(timezone=True))
    
    # Location information
    login_location = Column(String(100))
    login_country = Column(String(50))
