-- Initialize FleetSentinel database with PostGIS extension

-- Enable PostGIS extension
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;

-- Create indexes for spatial queries
-- These will be created by SQLAlchemy migrations, but we ensure PostGIS is ready

-- Grant necessary permissions
GRANT ALL PRIVILEGES ON DATABASE fleetsentinel TO fleetsentinel_user;
GRANT ALL ON SCHEMA public TO fleetsentinel_user;

-- Create initial spatial reference systems if needed
-- WGS84 (EPSG:4326) should already be available in PostGIS

-- Log successful initialization
SELECT 'FleetSentinel database initialized with PostGI<PERSON>' as status;
