// Type definitions for FleetSentinel application

export interface User {
  id: number
  username: string
  email: string
  fullName?: string
  role: 'user' | 'admin' | 'fleet_manager'
  isActive: boolean
}

export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
}

export interface LoginCredentials {
  username: string
  password: string
}

export interface LoginResponse {
  access_token: string
  token_type: string
  expires_in: number
  user?: User
}

// Route and navigation types
export interface Coordinates {
  lat: number
  lon: number
}

export interface RouteRequest {
  startLat: number
  startLon: number
  endLat: number
  endLon: number
  vehicleId?: string
  routeType?: 'fastest' | 'shortest' | 'safest'
}

export interface RouteSegment {
  id: string
  sequenceNumber: number
  geometry: number[][]
  distanceKm: number
  durationMinutes: number
  riskScore: number
  riskFactors: string[]
  roadType: string
  hasWeatherAlerts: boolean
  hasFloodZones: boolean
}

export interface Route {
  id: string
  routeId: string
  distanceKm: number
  durationMinutes: number
  riskScore: number
  coordinates: number[][]
  segments: RouteSegment[]
  alerts: Alert[]
  status: 'planned' | 'active' | 'completed' | 'cancelled'
}

// Risk and alert types
export interface RiskZone {
  id: string
  zoneType: 'flood' | 'weather' | 'terrain'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  geometry: GeoJSON.Polygon
  riskScore: number
  confidence: number
  isActive: boolean
  startTime?: string
  endTime?: string
}

export interface WeatherAlert {
  id: string
  alertType: string
  severity: 'minor' | 'moderate' | 'severe' | 'extreme'
  urgency: 'immediate' | 'expected' | 'future'
  certainty: 'observed' | 'likely' | 'possible'
  headline: string
  description: string
  instruction?: string
  onset?: string
  expires?: string
  geometry?: GeoJSON.Polygon
  affectedAreas: string[]
  isActive: boolean
}

export interface Alert {
  id: string
  type: 'weather' | 'flood' | 'traffic' | 'road_closure'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  message: string
  location: Coordinates
  timestamp: string
  isActive: boolean
}

// Vehicle types
export interface Vehicle {
  id: string
  vehicleId: string
  licensePlate?: string
  make?: string
  model?: string
  year?: number
  vehicleType: string
  isActive: boolean
  currentLocation?: Coordinates
  lastSeen?: string
}

// Map and visualization types
export interface MapViewState {
  longitude: number
  latitude: number
  zoom: number
  bearing?: number
  pitch?: number
}

export interface MapLayer {
  id: string
  type: 'risk-zones' | 'weather-alerts' | 'flood-zones' | 'routes'
  visible: boolean
  opacity: number
  data?: any
}

export interface FilterOptions {
  weatherTypes: string[]
  severityLevels: string[]
  timeWindow: {
    start: string
    end: string
  }
  riskThreshold: number
}

// API response types
export interface ApiResponse<T> {
  data: T
  message?: string
  error?: boolean
  statusCode?: number
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// Error types
export interface ApiError {
  message: string
  statusCode: number
  details?: any
}

// Dashboard types
export interface DashboardStats {
  totalRoutes: number
  activeAlerts: number
  highRiskZones: number
  averageRiskScore: number
  recentRoutes: Route[]
  criticalAlerts: Alert[]
}
